/* تنسيقات بطاقات الإجازات */

.vacation-cards-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  direction: rtl;
  text-align: right;
}

/* رأس الصفحة */
.page-header {
  text-align: center;
  margin-bottom: 2.5rem;
  padding: 1.5rem 0;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.page-header h1 i {
  margin-left: 1rem;
  color: #3498db;
}

.page-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

/* حاوية البطاقات - شبكة عادية */
.vacation-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 320px));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  justify-content: center;
}

/* تنسيق البطاقة - شكل عمودي (طولي) */
.vacation-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
  height: fit-content;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.vacation-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.vacation-card:hover::before {
  transform: scaleX(1);
}

.vacation-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 36px rgba(0, 0, 0, 0.12);
}

/* أيقونة البطاقة - في الأعلى للشكل العمودي */
.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.2rem auto;
  transition: all 0.3s ease;
}

.card-icon i {
  font-size: 1.6rem;
  color: #ffffff;
}

.vacation-card:hover .card-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

/* محتوى البطاقة */
.card-content {
  flex: 1;
}

.card-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.6rem;
  line-height: 1.3;
}

.card-description {
  font-size: 0.9rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 1.2rem;
}

/* ميزات البطاقة - عمودي */
.card-features {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #5a6c7d;
  font-weight: 500;
}

.feature-item i {
  margin-left: 0.5rem;
  color: #3498db;
  width: 16px;
  text-align: center;
}

/* سهم البطاقة - في الأسفل للشكل العمودي */
.card-arrow {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.card-arrow i {
  font-size: 1.2rem;
  color: #3498db;
}

.vacation-card:hover .card-arrow {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

/* رسالة عدم وجود صلاحيات */
.no-permission-message {
  text-align: center;
  background: #ffffff;
  border-radius: 16px;
  padding: 3rem 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 2rem auto;
}

.no-permission-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.no-permission-icon i {
  font-size: 2rem;
  color: #ffffff;
}

.no-permission-message h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 700;
}

.no-permission-message p {
  font-size: 1rem;
  color: #7f8c8d;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 2rem;
  }

  .vacation-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(250px, 300px));
    gap: 1.2rem;
    padding: 0 0.5rem;
  }

  .vacation-card {
    padding: 1.3rem;
    max-width: 300px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
  }

  .card-icon i {
    font-size: 1.3rem;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-arrow {
    display: none;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .vacation-cards-container {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .vacation-card {
    padding: 1.2rem;
    max-width: 100%;
  }

  .card-icon {
    width: 45px;
    height: 45px;
  }

  .card-icon i {
    font-size: 1.2rem;
  }

  .card-title {
    font-size: 1.1rem;
  }

  .card-description {
    font-size: 0.85rem;
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .no-permission-message {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
}

/* تأثيرات إضافية */
.vacation-card:active {
  transform: translateY(-2px) scale(0.98);
}

/* تحسين التخطيط للشاشات الكبيرة */
@media (min-width: 1200px) {
  .vacation-cards-container {
    grid-template-columns: repeat(3, 300px);
    max-width: 960px;
  }
}

/* تحسين التخطيط للشاشات المتوسطة */
@media (min-width: 769px) and (max-width: 1199px) {
  .vacation-cards-container {
    grid-template-columns: repeat(2, 300px);
    max-width: 640px;
  }
}

/* تنسيق خاص للبطاقات المخفية */
.vacation-card[style*="display: none"] {
  display: none !important;
}

/* تحسين الألوان لكل بطاقة */
.vacation-card:nth-child(1) .card-icon {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.vacation-card:nth-child(1):hover .card-icon {
  box-shadow: 0 8px 24px rgba(46, 204, 113, 0.3);
}

.vacation-card:nth-child(2) .card-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.vacation-card:nth-child(2):hover .card-icon {
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.vacation-card:nth-child(3) .card-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.vacation-card:nth-child(3):hover .card-icon {
  box-shadow: 0 8px 24px rgba(155, 89, 182, 0.3);
}
