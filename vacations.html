<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام الإجازات</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="vacations.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
</head>
<body class="vacations-page">

    <!-- زر العودة لإدارة الإجازات -->
  <div class="back-to-dashboard">
    <a href="vacation-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة الإجازات</span>
    </a>
  </div>





  <div class="main-content full-width" id="mainContent">
    <h1>نظام الإجازات</h1>

    <!-- تم إزالة أزرار التحكم في السيرفر -->

    <!-- تم نقل أزرار التبويبات إلى صفحة البطاقات vacation-cards.html -->

    <div class="tab-content" id="add-vacation">
      <div class="vacation-form">
        <div class="form-group">
          <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
        </div>

        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeDepartment">الإدارة:</label>
          <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
        </div>

        <div class="form-group">
          <label for="vacationType">نوع الإجازة:</label>
          <select id="vacationType" required>
            <option value="">اختر نوع الإجازة</option>
            <option value="casual">إجازة عارضة</option>
            <option value="permission">غياب بإذن</option>
            <option value="absence">غياب بدون إذن</option>
            <option value="annual">إجازة سنوية</option>
            <option value="unpaid">إجازة بدون راتب</option>
            <option value="sick">إجازة مرضية</option>
            <option value="official">إجازات خارج الرصيد</option>
          </select>
        </div>

        <div class="form-group" id="officialHolidayGroup" style="display: none;">
          <label for="officialHoliday">نوع الإجازة:</label>
          <select id="officialHoliday">
            <option value="">اختر نوع الإجازة</option>
            <option value="police_day">عيد الشرطة (25 يناير)</option>
            <option value="sinai_day">عيد تحرير سيناء</option>
            <option value="labor_day">عيد العمال</option>
            <option value="june_revolution">عيد ثورة 30 يونيو</option>
            <option value="july_revolution">عيد ثورة 23 يوليو</option>
            <option value="armed_forces_day">عيد القوات المسلحة (6 أكتوبر)</option>
            <option value="christmas">عيد الميلاد المجيد</option>
            <option value="sham_el_nessim">عيد شم النسيم</option>
            <option value="islamic_new_year">رأس السنة الهجرية</option>
            <option value="prophet_birthday">المولد النبوي الشريف</option>
            <option value="eid_adha">عيد الأضحى</option>
            <option value="eid_fitr">عيد الفطر</option>
            <option value="emergency">اجازة اضطرارية</option>
            <option value="birth">مولود</option>
            <option value="maternity">اجازة وضع</option>
            <option value="marriage">زواج</option>
            <option value="death_first_degree">الوفاة من الدرجة الاولى</option>
            <option value="military_service">الاستدعاء للجيش</option>
            <option value="exams">الامتحانات</option>
            <option value="pilgrimage">الحج والعمرة</option>
          </select>
        </div>

        <div class="form-group">
          <label for="vacationDate">تاريخ الإجازة:</label>
          <input type="date" id="vacationDate" required>
        </div>

        <div class="form-group" id="daysCountGroup">
          <label for="daysCount">عدد الأيام:</label>
          <input type="number" id="daysCount" min="1" value="1" required>
          <small style="color: #666; font-size: 0.9em;">سيتم إنشاء إجازة منفصلة لكل يوم</small>
          <div id="vacationEndDateInfo" style="margin-top: 8px; padding: 8px; background-color: #e8f5e8; border-radius: 4px; font-size: 0.9em; color: #2e7d32; display: none;">
            <strong>📅 تاريخ نهاية الإجازة (استرشادي): </strong>
            <span id="calculatedEndDate">-</span>
          </div>
        </div>

        <div class="form-actions">
          <button id="saveVacation" class="save-btn">حفظ الإجازة</button>
          <button id="resetForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>
      
      <!-- جدول الإجازات المضافة -->
      <div class="added-vacations-container">
        <h3>الإجازات المضافة</h3>

        <!-- فلاتر البحث المحددة للإجازات المضافة -->
        <div class="search-filters-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterVacationId">كود الإجازة:</label>
              <input type="text" id="filterVacationId" placeholder="أدخل كود الإجازة" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterVacationEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterVacationEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterVacationEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterVacationEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterVacationFromDate">من تاريخ:</label>
              <input type="date" id="filterVacationFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterVacationToDate">إلى تاريخ:</label>
              <input type="date" id="filterVacationToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyVacationFiltersBtn" class="search-btn">تطبيق الفلاتر</button>
            <button id="clearVacationFiltersBtn" class="reset-btn">مسح الفلاتر</button>
          </div>
        </div>

        <table class="added-vacations-table">
          <thead>
            <tr>
              <th>كود الإجازة</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>نوع الإجازة</th>
              <th>تاريخ الإجازة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="addedVacationsTableBody"></tbody>
        </table>
      </div>
    </div>

    <div class="tab-content" id="view-vacations" style="display: none;">
      <div class="search-container">
        <div class="search-filters">
          <div class="date-filter">
            <label for="startDate">من تاريخ:</label>
            <input type="date" id="startDate">
          </div>
          <div class="date-filter">
            <label for="endDate">إلى تاريخ:</label>
            <input type="date" id="endDate">
          </div>
          <select id="departmentFilterView">
            <option value="">كل الإدارات</option>
          </select>
          <div class="name-filter">
            <label for="nameSearch">بحث بالاسم:</label>
            <input type="text" id="nameSearch" placeholder="ادخل اسم الموظف">
          </div>
          <button id="searchVacationsBtn" class="search-btn">بحث</button>
          <button id="resetVacationsBtn" class="reset-btn">إعادة تعيين</button>
          <button id="showAllVacationsBtn" class="show-all-btn">عرض الكل</button>
          <div class="separator"></div>
          <button id="clearAllVacationsBtn" class="clear-all-btn danger-btn">مسح جميع الإجازات</button>
        </div>
      </div>

      <div class="vacations-table-container">
        <div class="table-controls">
          <button id="exportVacationsBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="vacations-table">
          <thead>
            <tr>
              <th>الكود</th>
              <th>الاسم</th>
              <th>عارضة</th>
              <th>غياب بإذن</th>
              <th>غياب بدون إذن</th>
              <th>سنوية</th>
              <th>بدون راتب</th>
              <th>مرضي</th>
              <th>إجازات خارج الرصيد</th>
              <th>التفاصيل</th>
            </tr>
          </thead>
          <tbody id="vacationsTableBody"></tbody>
        </table>
      </div>
    </div>

    <!-- قسم تقارير الإجازات -->
    <div class="tab-content" id="vacation-reports" style="display: none;">
      <div class="vacation-reports-container">
        <h2>تقارير الإجازات</h2>
        
        <div class="reports-tabs">
          <button class="report-tab-btn active" data-report="employee-summary">تقرير إجمالي الإجازات حسب الموظف</button>
          <button class="report-tab-btn" data-report="department-summary">تقرير الإجازات حسب الإدارة</button>
          <button class="report-tab-btn" data-report="absence-report">تقرير الغياب المتكرر</button>
          <button class="report-tab-btn" data-report="highest-absence-days">تقرير الأيام الأعلى غيابًا</button>
          <button class="report-tab-btn" data-report="top-vacation-users">تقرير الموظفين الأعلى استخدامًا للإجازات</button>
          <button class="report-tab-btn" data-report="custom-employee">تقرير مخصص لموظف معين</button>
        </div>

        <!-- تقرير إجمالي الإجازات حسب الموظف -->
        <div class="report-content" id="employee-summary">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="employeeSummaryDept">الإدارة:</label>
              <select id="employeeSummaryDept">
                <option value="">كل الإدارات</option>
              </select>
            </div>
            <div class="vacation-filter-group">
              <label for="employeeSummaryStartDate">من تاريخ:</label>
              <input type="date" id="employeeSummaryStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="employeeSummaryEndDate">إلى تاريخ:</label>
              <input type="date" id="employeeSummaryEndDate">
            </div>
            <div class="vacation-filter-group">
              <label for="employeeSummaryYear">السنة:</label>
              <select id="employeeSummaryYear">
                <option value="">كل السنوات</option>
              </select>
            </div>
            <button id="generateEmployeeSummaryBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportEmployeeSummaryBtn" class="export-btn">تصدير إلى Excel</button>
          </div>
          <div class="report-table-container">
            <table class="report-table" id="employeeSummaryTable">
              <thead>
                <tr>
                  <th>كود الموظف</th>
                  <th>اسم الموظف</th>
                  <th>الإدارة</th>
                  <th>عارضة</th>
                  <th>سنوية</th>
                  <th>غياب بإذن</th>
                  <th>بدون إذن</th>
                  <th>مرضية</th>
                  <th>بدون راتب</th>
                  <th>إجازات خارج الرصيد</th>
                  <th>إجمالي الأيام</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody id="employeeSummaryTableBody"></tbody>
            </table>
          </div>
        </div>

        <!-- تقرير الإجازات حسب الإدارة -->
        <div class="report-content" id="department-summary" style="display: none;">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="deptSummaryStartDate">من تاريخ:</label>
              <input type="date" id="deptSummaryStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="deptSummaryEndDate">إلى تاريخ:</label>
              <input type="date" id="deptSummaryEndDate">
            </div>
            <div class="vacation-filter-group">
              <label for="deptSummaryVacationType">نوع الإجازة:</label>
              <select id="deptSummaryVacationType">
                <option value="">كل الأنواع</option>
                <option value="casual">إجازة عارضة</option>
                <option value="permission">غياب بإذن</option>
                <option value="absence">غياب بدون إذن</option>
                <option value="annual">إجازة سنوية</option>
                <option value="unpaid">إجازة بدون راتب</option>
                <option value="sick">إجازة مرضية</option>
                <option value="official">إجازات خارج الرصيد</option>
              </select>
            </div>
            <button id="generateDeptSummaryBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportDeptSummaryBtn" class="export-btn">تصدير إلى Excel</button>
          </div>
          <div class="report-table-container">
            <table class="report-table" id="deptSummaryTable">
              <thead>
                <tr>
                  <th>الإدارة</th>
                  <th>عدد الموظفين</th>
                  <th>عدد الإجازات</th>
                  <th>إجمالي الأيام</th>
                  <th>متوسط الأيام لكل موظف</th>
                </tr>
              </thead>
              <tbody id="deptSummaryTableBody"></tbody>
            </table>
          </div>
        </div>

        <!-- تقرير الغياب المتكرر -->
        <div class="report-content" id="absence-report" style="display: none;">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="absenceDepartment">الإدارة:</label>
              <select id="absenceDepartment">
                <option value="">كل الإدارات</option>
              </select>
            </div>
            <div class="vacation-filter-group">
              <label for="absenceMinDays">الحد الأدنى لأيام الغياب بدون إذن:</label>
              <input type="number" id="absenceMinDays" value="3" min="1">
            </div>
            <div class="vacation-filter-group">
              <label for="absenceStartDate">من تاريخ:</label>
              <input type="date" id="absenceStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="absenceEndDate">إلى تاريخ:</label>
              <input type="date" id="absenceEndDate">
            </div>
            <button id="generateAbsenceReportBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportAbsenceReportBtn" class="export-btn">تصدير إلى Excel</button>
          </div>
          <div class="report-table-container">
            <table class="report-table" id="absenceReportTable">
              <thead>
                <tr>
                  <th>كود الموظف</th>
                  <th>اسم الموظف</th>
                  <th>الإدارة</th>
                  <th>عدد أيام الغياب بدون إذن</th>
                  <th>عدد مرات الغياب</th>
                  <th>آخر غياب</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody id="absenceReportTableBody"></tbody>
            </table>
          </div>
        </div>

        <!-- تقرير الأيام الأعلى غيابًا -->
        <div class="report-content" id="highest-absence-days" style="display: none;">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="absenceDaysStartDate">من تاريخ:</label>
              <input type="date" id="absenceDaysStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="absenceDaysEndDate">إلى تاريخ:</label>
              <input type="date" id="absenceDaysEndDate">
            </div>
            <div class="vacation-filter-group">
              <label for="absenceDaysType">نوع التحليل:</label>
              <select id="absenceDaysType">
                <option value="specific_dates">تواريخ محددة</option>
                <option value="weekdays">أيام الأسبوع</option>
              </select>
            </div>
            <button id="generateAbsenceDaysReportBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportAbsenceDaysReportBtn" class="export-btn">تصدير إلى Excel</button>
          </div>

          <div class="report-table-container">
            <table id="absenceDaysReportTable" class="report-table">
              <thead>
                <tr>
                  <th>التاريخ/اليوم</th>
                  <th>عدد حالات الغياب</th>
                  <th>إجمالي الموظفين الغائبين</th>
                  <th>النسبة المئوية</th>
                </tr>
              </thead>
              <tbody id="absenceDaysReportTableBody">
                <tr>
                  <td colspan="4" class="no-results">لا توجد بيانات لعرضها</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- تقرير الموظفين الأعلى استخدامًا للإجازات -->
        <div class="report-content" id="top-vacation-users" style="display: none;">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="topUsersStartDate">من تاريخ:</label>
              <input type="date" id="topUsersStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="topUsersEndDate">إلى تاريخ:</label>
              <input type="date" id="topUsersEndDate">
            </div>
            <div class="vacation-filter-group">
              <label for="topUsersDepartment">الإدارة:</label>
              <select id="topUsersDepartment">
                <option value="">كل الإدارات</option>
              </select>
            </div>
            <div class="vacation-filter-group">
              <label for="topUsersLimit">عدد الموظفين:</label>
              <select id="topUsersLimit">
                <option value="10">أعلى 10 موظفين</option>
                <option value="20">أعلى 20 موظف</option>
                <option value="50">أعلى 50 موظف</option>
                <option value="all">جميع الموظفين</option>
              </select>
            </div>
            <button id="generateTopUsersReportBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportTopUsersReportBtn" class="export-btn">تصدير إلى Excel</button>
          </div>

          <div class="report-table-container">
            <table id="topUsersReportTable" class="report-table">
              <thead>
                <tr>
                  <th>الترتيب</th>
                  <th>كود الموظف</th>
                  <th>اسم الموظف</th>
                  <th>الإدارة</th>
                  <th>إجمالي أيام الإجازات</th>
                  <th>عدد الإجازات</th>
                  <th>متوسط أيام الإجازة</th>
                  <th>النوع الأكثر استخداماً</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody id="topUsersReportTableBody">
                <tr>
                  <td colspan="9" class="no-results">لا توجد بيانات لعرضها</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- تقرير مخصص لموظف معين -->
        <div class="report-content" id="custom-employee" style="display: none;">
          <div class="vacation-report-filters">
            <div class="vacation-filter-group">
              <label for="customEmployeeSearch">البحث عن الموظف:</label>
              <input type="text" id="customEmployeeSearch" placeholder="أدخل اسم أو كود الموظف" list="customEmployeeSuggestions" autocomplete="off">
              <datalist id="customEmployeeSuggestions"></datalist>
            </div>
            <div class="vacation-filter-group">
              <label for="customEmployeeStartDate">من تاريخ:</label>
              <input type="date" id="customEmployeeStartDate">
            </div>
            <div class="vacation-filter-group">
              <label for="customEmployeeEndDate">إلى تاريخ:</label>
              <input type="date" id="customEmployeeEndDate">
            </div>
            <div class="vacation-filter-group">
              <label for="customEmployeeYear">السنة:</label>
              <select id="customEmployeeYear">
                <option value="">كل السنوات</option>
              </select>
            </div>
            <button id="generateCustomEmployeeBtn" class="generate-btn">إنشاء التقرير</button>
            <button id="exportCustomEmployeeBtn" class="export-btn">تصدير إلى Excel</button>
            <button id="printCustomEmployeeBtn" class="print-btn">طباعة</button>
          </div>
          
          <div id="customEmployeeInfo" class="employee-info-card" style="display: none;">
            <h3>معلومات الموظف</h3>
            <div class="info-grid">
              <div class="info-item">
                <label>كود الموظف:</label>
                <span id="customEmployeeCode"></span>
              </div>
              <div class="info-item">
                <label>اسم الموظف:</label>
                <span id="customEmployeeName"></span>
              </div>
              <div class="info-item">
                <label>الإدارة:</label>
                <span id="customEmployeeDept"></span>
              </div>
              <div class="info-item">
                <label>رصيد الإجازات:</label>
                <span id="customEmployeeBalance"></span>
              </div>
              <div class="info-item">
                <label>المستخدم:</label>
                <span id="customEmployeeUsed"></span>
              </div>
              <div class="info-item">
                <label>المتبقي:</label>
                <span id="customEmployeeRemaining"></span>
              </div>
            </div>
          </div>
          
          <div class="report-table-container">
            <div id="customEmployeePreviousYearNote" class="previous-year-note" style="display: none; margin-bottom: 10px; font-size: 0.9em; color: #c62828; font-weight: bold; text-align: center;">
              🔴 الحقول باللون الأحمر: إجازات من العام المالي السابق (قبل 26-6)
            </div>
            <table class="report-table" id="customEmployeeTable">
              <thead>
                <tr>
                  <th>نوع الإجازة</th>
                  <th>فترة الإجازة</th>
                  <th>عدد الأيام</th>
                  <th>نوع الإجازة</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody id="customEmployeeTableBody"></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>


  </div>

  <!-- تم إزالة النافذة المنبثقة لتغيير السيرفر -->

  <!-- نافذة منبثقة لعرض تفاصيل الإجازات -->
  <div id="vacationDetailsModal" class="modal">
    <div class="modal-content">
      <h2>تفاصيل الإجازات</h2>
      <button class="close-details-btn">إغلاق</button>
      <div id="employeeInfo">
        <p><strong>الكود:</strong> <span id="detailsEmployeeCode"></span></p>
        <p><strong>الاسم:</strong> <span id="detailsEmployeeFullName"></span></p>
        <p><strong>الإدارة:</strong> <span id="detailsEmployeeDepartment"></span></p>
        <div class="vacation-balance-info">
          <h3>معلومات الإجازات</h3>
          <div class="vacation-balance-cards">
            <div class="balance-card total">
              <div class="balance-card-icon">📊</div>
              <div class="balance-card-label">رصيد الإجازات</div>
              <div class="balance-card-value" id="detailsLeaveBalance">30</div>
            </div>
            <div class="balance-card used">
              <div class="balance-card-icon">📅</div>
              <div class="balance-card-label">الإجازات المستخدمة</div>
              <div class="balance-card-value" id="detailsLeaveUsed">0</div>
            </div>
            <div class="balance-card remaining">
              <div class="balance-card-icon">✨</div>
              <div class="balance-card-label">الإجازات المتبقية</div>
              <div class="balance-card-value" id="detailsLeaveRemaining">30</div>
            </div>
          </div>
        </div>
      </div>
      <div class="details-actions">
        <button id="printVacationDetails" class="print-btn">طباعة</button>
        <button id="exportVacationDetailsToExcel" class="export-details-btn">تصدير إلى إكسل</button>
        <div id="detailsPreviousYearNote" class="previous-year-note" style="display: none; margin-right: 20px; font-size: 0.9em; color: #c62828; font-weight: bold;">
          🔴 الحقول باللون الأحمر: إجازات من العام المالي السابق (قبل 26-6)
        </div>
      </div>
      <div class="vacations-details-container">
        <table class="vacations-details-table">
          <thead>
            <tr>
              <th>نوع الإجازة</th>
              <th>التاريخ</th>
              <th>عدد الأيام</th>
              <th>نوع الإجازة</th>
            </tr>
          </thead>
          <tbody id="vacationDetailsTableBody"></tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- نافذة منبثقة لتعديل الإجازة المضافة -->
  <div id="editAddedVacationModal" class="modal">
    <div class="modal-content">
      <span class="close-modal" id="closeEditAddedVacationModal">&times;</span>
      <h2>تعديل الإجازة</h2>
      <div class="vacation-form">
        <input type="hidden" id="editVacationId">
        <div class="form-group">
          <label for="editVacationEmployeeCode">كود الموظف:</label>
          <input type="text" id="editVacationEmployeeCode" readonly>
        </div>
        <div class="form-group">
          <label for="editVacationEmployeeName">اسم الموظف:</label>
          <input type="text" id="editVacationEmployeeName" readonly>
        </div>
        <div class="form-group">
          <label for="editVacationEmployeeDepartment">الإدارة:</label>
          <input type="text" id="editVacationEmployeeDepartment" readonly>
        </div>
        <div class="form-group">
          <label for="editVacationType">نوع الإجازة:</label>
          <select id="editVacationType" required>
            <option value="">اختر نوع الإجازة</option>
            <option value="casual">إجازة عارضة</option>
            <option value="permission">غياب بإذن</option>
            <option value="absence">غياب بدون إذن</option>
            <option value="annual">إجازة سنوية</option>
            <option value="unpaid">إجازة بدون راتب</option>
            <option value="sick">إجازة مرضية</option>
            <option value="official">إجازات خارج الرصيد</option>
          </select>
        </div>
        <div class="form-group" id="editOfficialHolidayGroup" style="display: none;">
          <label for="editOfficialHoliday">نوع الإجازة:</label>
          <select id="editOfficialHoliday">
            <option value="">اختر نوع الإجازة</option>
            <option value="عيد الشرطة (25 يناير)">عيد الشرطة (25 يناير)</option>
            <option value="عيد تحرير سيناء">عيد تحرير سيناء</option>
            <option value="عيد العمال">عيد العمال</option>
            <option value="عيد ثورة 30 يونيو">عيد ثورة 30 يونيو</option>
            <option value="عيد ثورة 23 يوليو">عيد ثورة 23 يوليو</option>
            <option value="عيد القوات المسلحة (6 أكتوبر)">عيد القوات المسلحة (6 أكتوبر)</option>
            <option value="عيد الميلاد المجيد">عيد الميلاد المجيد</option>
            <option value="عيد شم النسيم">عيد شم النسيم</option>
            <option value="رأس السنة الهجرية">رأس السنة الهجرية</option>
            <option value="المولد النبوي الشريف">المولد النبوي الشريف</option>
            <option value="عيد الأضحى">عيد الأضحى</option>
            <option value="عيد الفطر">عيد الفطر</option>
            <option value="اجازة اضطرارية">اجازة اضطرارية</option>
            <option value="مولود">مولود</option>
            <option value="اجازة وضع">اجازة وضع</option>
            <option value="زواج">زواج</option>
            <option value="الوفاة من الدرجة الاولى">الوفاة من الدرجة الاولى</option>
            <option value="الاستدعاء للجيش">الاستدعاء للجيش</option>
            <option value="الامتحانات">الامتحانات</option>
            <option value="الحج والعمرة">الحج والعمرة</option>
          </select>
        </div>
        <div class="form-group">
          <label for="editVacationDate">تاريخ الإجازة:</label>
          <input type="date" id="editVacationDate" required>
        </div>
        <div class="form-actions">
          <button id="updateVacationBtn" class="save-btn">تحديث الإجازة</button>
          <button id="cancelEditVacationBtn" class="cancel-btn">إلغاء</button>
        </div>
      </div>
    </div>
  </div>



  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="vacations.js"></script>

  

<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

// تحميل القائمة الجانبية مع النظام المحسن
</script>
</body>
</html>