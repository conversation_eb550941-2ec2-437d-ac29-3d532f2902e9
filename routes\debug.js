const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// إنشاء جدول المستخدمين إذا لم يكن موجودًا
const createUsersTable = async (pool) => {
  try {
    // التحقق من وجود جدول المستخدمين
    const [tables] = await pool.promise().query(
      "SHOW TABLES LIKE 'users'"
    );
    
    if (tables.length === 0) {
      // إنشاء جدول المستخدمين
      await pool.promise().query(`
        CREATE TABLE users (
          id int NOT NULL AUTO_INCREMENT,
          username varchar(50) NOT NULL,
          password varchar(255) NOT NULL,
          permissions JSON DEFAULT NULL,
          created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY username (username)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
      `);
      
      // إنشاء مستخدم admin افتراضي
      const hashedPassword = await bcrypt.hash('123456', 10);
      await pool.promise().query(
        "INSERT INTO users (username, password, permissions) VALUES (?, ?, ?)",
        [
          'admin', 
          hashedPassword, 
          JSON.stringify({
            can_view: true,
            can_add: true,
            can_edit: true,
            can_delete: true,
            view_employees: true,
            view_vacations: true,
            add_vacation: true,
            view_vacations_list: true,
            view_vacation_reports: true,
            view_contributions: true,
            view_rewards_list: true,
            add_reward: true,
            export_rewards: true,
            view_deductions_list: true,
            add_deduction: true,
            export_deductions: true,
            view_resignations: true,
            add_resignation: true,
            edit_resignation: true,
            delete_resignation: true,
            view_training: true,
            add_training: true,
            edit_training: true,
            delete_training: true,
            view_salary_advances: true,
            add_salary_advance: true,
            edit_salary_advance: true,
            delete_salary_advance: true,
            view_extra_hours: true,
            add_extra_hours: true,
            edit_extra_hours: true,
            delete_extra_hours: true,
            view_activity_logs: true
          })
        ]
      );
      
      console.log('تم إنشاء جدول المستخدمين ومستخدم admin افتراضي');
    }
  } catch (error) {
    console.error('خطأ في إنشاء جدول المستخدمين:', error);
    throw error;
  }
};

// تحديث صلاحيات المستخدم admin لتشمل الاستقالات
router.get('/update-admin-permissions', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createUsersTable(pool);

    // تحديث صلاحيات admin
    const updatedPermissions = {
      can_view: true,
      can_add: true,
      can_edit: true,
      can_delete: true,
      view_employees: true,
      view_vacations: true,
      add_vacation: true,
      view_vacations_list: true,
      view_vacation_reports: true,
      view_contributions: true,
      view_rewards_list: true,
      add_reward: true,
      export_rewards: true,
      view_deductions_list: true,
      add_deduction: true,
      export_deductions: true,
      view_resignations: true,
      add_resignation: true,
      edit_resignation: true,
      delete_resignation: true,
      view_training: true,
      add_training: true,
      edit_training: true,
      delete_training: true,
      view_salary_advances: true,
      add_salary_advance: true,
      edit_salary_advance: true,
      delete_salary_advance: true,
      view_extra_hours: true,
      add_extra_hours: true,
      edit_extra_hours: true,
      delete_extra_hours: true,
      view_activity_logs: true
    };

    await pool.promise().query(
      "UPDATE users SET permissions = ? WHERE username = 'admin'",
      [JSON.stringify(updatedPermissions)]
    );

    res.json({ 
      message: 'تم تحديث صلاحيات المستخدم admin بنجاح',
      permissions: updatedPermissions
    });
  } catch (error) {
    console.error('خطأ في تحديث صلاحيات admin:', error);
    res.status(500).json({ error: 'فشل في تحديث الصلاحيات' });
  }
});

// endpoint للتحقق من الـ triggers
router.get('/triggers', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [triggers] = await pool.promise().query("SHOW TRIGGERS");
    res.json(triggers);
  } catch (error) {
    console.error('خطأ في جلب الـ triggers:', error);
    res.status(500).json({ error: 'فشل في جلب الـ triggers' });
  }
});

// endpoint للتحقق من بنية جدول الموظفين
router.get('/employees-structure', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [structure] = await pool.promise().query("DESCRIBE employees");
    res.json(structure);
  } catch (error) {
    console.error('خطأ في جلب بنية جدول الموظفين:', error);
    res.status(500).json({ error: 'فشل في جلب بنية الجدول' });
  }
});

// endpoint لإضافة حقل status إذا لم يكن موجوداً
router.post('/add-status-field', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    // التحقق من وجود حقل status
    const [columns] = await pool.promise().query("SHOW COLUMNS FROM employees LIKE 'status'");
    
    if (columns.length === 0) {
      // إضافة حقل status
      await pool.promise().query("ALTER TABLE employees ADD COLUMN status ENUM('نشط', 'مستقيل') DEFAULT 'نشط'");
      
      // تحديث جميع الموظفين الحاليين ليكونوا نشطين
      await pool.promise().query("UPDATE employees SET status = 'نشط' WHERE status IS NULL");
      
      res.json({ 
        message: 'تم إضافة حقل status بنجاح وتحديث جميع الموظفين الحاليين ليكونوا نشطين',
        added: true
      });
    } else {
      res.json({ 
        message: 'حقل status موجود بالفعل',
        added: false
      });
    }
  } catch (error) {
    console.error('خطأ في إضافة حقل status:', error);
    res.status(500).json({ error: 'فشل في إضافة حقل status' });
  }
});

// endpoint لإنشاء الـ triggers
router.post('/create-triggers', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    // حذف الـ triggers الموجودة
    await pool.promise().query("DROP TRIGGER IF EXISTS update_employee_status_on_resignation");
    await pool.promise().query("DROP TRIGGER IF EXISTS restore_employee_status_on_resignation_delete");

    // إنشاء trigger لتحديث حالة الموظف عند إضافة استقالة
    await pool.promise().query(`
      CREATE TRIGGER update_employee_status_on_resignation
      AFTER INSERT ON resignations
      FOR EACH ROW
      BEGIN
        UPDATE employees SET status = 'مستقيل' WHERE code = NEW.employee_code;
      END
    `);

    // إنشاء trigger لاستعادة حالة الموظف عند حذف الاستقالة
    await pool.promise().query(`
      CREATE TRIGGER restore_employee_status_on_resignation_delete
      AFTER DELETE ON resignations
      FOR EACH ROW
      BEGIN
        UPDATE employees SET status = 'نشط' WHERE code = OLD.employee_code;
      END
    `);

    res.json({ 
      message: 'تم إنشاء الـ triggers بنجاح',
      triggers: [
        'update_employee_status_on_resignation',
        'restore_employee_status_on_resignation_delete'
      ]
    });
  } catch (error) {
    console.error('خطأ في إنشاء الـ triggers:', error);
    res.status(500).json({ error: 'فشل في إنشاء الـ triggers' });
  }
});

// endpoint لاختبار تحديث حالة الموظف
router.post('/test-employee-status-update', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { employee_code, new_status } = req.body;

    if (!employee_code || !new_status) {
      return res.status(400).json({ error: 'كود الموظف والحالة الجديدة مطلوبان' });
    }

    if (!['نشط', 'مستقيل'].includes(new_status)) {
      return res.status(400).json({ error: 'الحالة يجب أن تكون "نشط" أو "مستقيل"' });
    }

    // التحقق من وجود الموظف
    const [employee] = await pool.promise().query(
      "SELECT code, full_name, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employee.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const oldStatus = employee[0].status;

    // تحديث حالة الموظف
    await pool.promise().query(
      "UPDATE employees SET status = ? WHERE code = ?",
      [new_status, employee_code]
    );

    res.json({
      message: 'تم تحديث حالة الموظف بنجاح',
      employee: {
        code: employee_code,
        name: employee[0].full_name,
        old_status: oldStatus,
        new_status: new_status
      }
    });
  } catch (error) {
    console.error('خطأ في تحديث حالة الموظف:', error);
    res.status(500).json({ error: 'فشل في تحديث حالة الموظف' });
  }
});

module.exports = router;
