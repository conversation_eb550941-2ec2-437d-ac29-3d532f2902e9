const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const { authenticateToken, checkPermission } = require("../middleware/auth");

const router = express.Router();

// إعداد multer لرفع الصور
const photoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/photos');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const employeeCode = req.params.employeeCode;
    const ext = path.extname(file.originalname);
    cb(null, `employee_${employeeCode}_photo${ext}`);
  }
});

const uploadEmployeePhoto = multer({
  storage: photoStorage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('يُسمح فقط بملفات الصور (JPEG, JPG, PNG, GIF)'));
    }
  }
});

// إعداد multer لرفع المستندات
const documentStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const employeeCode = req.params.employeeCode;
    const uploadPath = path.join(__dirname, '../uploads/documents', employeeCode);
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

const uploadEmployeeDocuments = multer({
  storage: documentStorage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// رفع صورة الموظف
router.post('/employees/:employeeCode/photo', authenticateToken, uploadEmployeePhoto.single('photo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'لم يتم رفع أي ملف' });
    }

    const employeeCode = req.params.employeeCode;
    const photoPath = `/uploads/photos/${req.file.filename}`;

    // تحديث مسار الصورة في قاعدة البيانات
    const pool = req.app.locals.pool;
    await pool.promise().query(
      "UPDATE employees SET photo = ? WHERE code = ?",
      [photoPath, employeeCode]
    );

    res.json({
      message: 'تم رفع الصورة بنجاح',
      photoPath: photoPath
    });
  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء رفع الصورة' });
  }
});

// رفع مستندات الموظف
router.post('/employees/:employeeCode/documents', authenticateToken, (req, res) => {
  const upload = uploadEmployeeDocuments.array('documents', 10);
  
  upload(req, res, async function (err) {
    if (err) {
      console.error('خطأ في رفع المستندات:', err);
      return res.status(400).json({ error: err.message });
    }

    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ error: 'لم يتم رفع أي ملفات' });
      }

      const employeeCode = req.params.employeeCode;
      const pool = req.app.locals.pool;
      
      // الحصول على المستندات الحالية
      const [currentDocs] = await pool.promise().query(
        "SELECT documents FROM employees WHERE code = ?",
        [employeeCode]
      );

      let existingDocuments = [];
      if (currentDocs.length > 0 && currentDocs[0].documents) {
        try {
          existingDocuments = JSON.parse(currentDocs[0].documents) || [];
        } catch (e) {
          existingDocuments = [];
        }
      }

      // إضافة المستندات الجديدة
      const newDocuments = req.files.map(file => ({
        id: Date.now() + Math.random(),
        name: file.originalname,
        path: `/uploads/documents/${employeeCode}/${file.filename}`,
        uploadDate: new Date().toISOString(),
        size: file.size
      }));

      const allDocuments = [...existingDocuments, ...newDocuments];

      // تحديث قاعدة البيانات
      await pool.promise().query(
        "UPDATE employees SET documents = ? WHERE code = ?",
        [JSON.stringify(allDocuments), employeeCode]
      );

      res.json({
        message: 'تم رفع المستندات بنجاح',
        documents: newDocuments
      });
    } catch (error) {
      console.error('خطأ في رفع المستندات:', error);
      res.status(500).json({ error: 'حدث خطأ أثناء رفع المستندات' });
    }
  });
});

// الحصول على صورة الموظف
router.get('/employees/:employeeCode/photo', async (req, res) => {
  try {
    const employeeCode = req.params.employeeCode;
    const pool = req.app.locals.pool;
    
    const [rows] = await pool.promise().query(
      "SELECT photo FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0 || !rows[0].photo) {
      return res.status(404).json({ error: 'لم يتم العثور على صورة للموظف' });
    }

    const photoPath = path.join(__dirname, '..', rows[0].photo);
    
    if (!fs.existsSync(photoPath)) {
      return res.status(404).json({ error: 'ملف الصورة غير موجود' });
    }

    res.sendFile(photoPath);
  } catch (error) {
    console.error('خطأ في جلب الصورة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب الصورة' });
  }
});

// الحصول على مستندات الموظف
router.get('/employees/:employeeCode/documents', async (req, res) => {
  try {
    const employeeCode = req.params.employeeCode;
    const pool = req.app.locals.pool;
    
    const [rows] = await pool.promise().query(
      "SELECT documents FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    let documents = [];
    if (rows[0].documents) {
      try {
        documents = JSON.parse(rows[0].documents) || [];
      } catch (e) {
        documents = [];
      }
    }

    res.json({ documents });
  } catch (error) {
    console.error('خطأ في جلب المستندات:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب المستندات' });
  }
});

// تحميل مستند محدد
router.get('/download/employees/:employeeCode/documents/:documentId', async (req, res) => {
  try {
    const { employeeCode, documentId } = req.params;
    const pool = req.app.locals.pool;
    
    const [rows] = await pool.promise().query(
      "SELECT documents FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    let documents = [];
    if (rows[0].documents) {
      try {
        documents = JSON.parse(rows[0].documents) || [];
      } catch (e) {
        return res.status(500).json({ error: 'خطأ في قراءة بيانات المستندات' });
      }
    }

    const document = documents.find(doc => doc.id == documentId);
    if (!document) {
      return res.status(404).json({ error: 'المستند غير موجود' });
    }

    const filePath = path.join(__dirname, '..', document.path);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'ملف المستند غير موجود' });
    }

    res.download(filePath, document.name);
  } catch (error) {
    console.error('خطأ في تحميل المستند:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحميل المستند' });
  }
});

// تحميل صورة الموظف
router.get('/download/employees/:employeeCode/photo', async (req, res) => {
  try {
    const employeeCode = req.params.employeeCode;
    const pool = req.app.locals.pool;
    
    const [rows] = await pool.promise().query(
      "SELECT photo, full_name FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0 || !rows[0].photo) {
      return res.status(404).json({ error: 'لم يتم العثور على صورة للموظف' });
    }

    const photoPath = path.join(__dirname, '..', rows[0].photo);
    
    if (!fs.existsSync(photoPath)) {
      return res.status(404).json({ error: 'ملف الصورة غير موجود' });
    }

    const ext = path.extname(photoPath);
    const filename = `${rows[0].full_name}_photo${ext}`;
    
    res.download(photoPath, filename);
  } catch (error) {
    console.error('خطأ في تحميل الصورة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحميل الصورة' });
  }
});

// حذف صورة الموظف
router.delete('/employees/:employeeCode/photo', authenticateToken, async (req, res) => {
  try {
    const employeeCode = req.params.employeeCode;
    const pool = req.app.locals.pool;
    
    // الحصول على مسار الصورة الحالية
    const [rows] = await pool.promise().query(
      "SELECT photo FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    if (rows[0].photo) {
      const photoPath = path.join(__dirname, '..', rows[0].photo);
      
      // حذف الملف من النظام
      if (fs.existsSync(photoPath)) {
        fs.unlinkSync(photoPath);
      }
    }

    // تحديث قاعدة البيانات
    await pool.promise().query(
      "UPDATE employees SET photo = NULL WHERE code = ?",
      [employeeCode]
    );

    res.json({ message: 'تم حذف الصورة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الصورة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف الصورة' });
  }
});

// حذف مستند محدد
router.delete('/employees/:employeeCode/documents/:documentId', authenticateToken, async (req, res) => {
  try {
    const { employeeCode, documentId } = req.params;
    const pool = req.app.locals.pool;
    
    // الحصول على المستندات الحالية
    const [rows] = await pool.promise().query(
      "SELECT documents FROM employees WHERE code = ?",
      [employeeCode]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    let documents = [];
    if (rows[0].documents) {
      try {
        documents = JSON.parse(rows[0].documents) || [];
      } catch (e) {
        return res.status(500).json({ error: 'خطأ في قراءة بيانات المستندات' });
      }
    }

    const documentIndex = documents.findIndex(doc => doc.id == documentId);
    if (documentIndex === -1) {
      return res.status(404).json({ error: 'المستند غير موجود' });
    }

    const document = documents[documentIndex];
    const filePath = path.join(__dirname, '..', document.path);
    
    // حذف الملف من النظام
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // إزالة المستند من القائمة
    documents.splice(documentIndex, 1);

    // تحديث قاعدة البيانات
    await pool.promise().query(
      "UPDATE employees SET documents = ? WHERE code = ?",
      [JSON.stringify(documents), employeeCode]
    );

    res.json({ message: 'تم حذف المستند بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المستند:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف المستند' });
  }
});

// عرض الملفات العامة
router.get('/files/*', (req, res) => {
  try {
    const filePath = req.params[0];
    const fullPath = path.join(__dirname, '../uploads', filePath);
    
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ error: 'الملف غير موجود' });
    }

    res.sendFile(fullPath);
  } catch (error) {
    console.error('خطأ في عرض الملف:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء عرض الملف' });
  }
});

module.exports = router;
